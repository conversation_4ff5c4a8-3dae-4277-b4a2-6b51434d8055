import { useState, useEffect, useCallback } from "react";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { InputTextarea } from "primereact/inputtextarea";
import { MultiSelect } from "primereact/multiselect";
import { Dropdown } from "primereact/dropdown";
import { But<PERSON> } from "primereact/button";
import { motion, AnimatePresence } from "framer-motion";
import { useLayout } from '@contexts/LayoutContext';
import { useNavigate } from 'react-router-dom';
import axiosInstance from "../../../config/Axios";
import PropTypes from 'prop-types';
import { FaBluetooth, FaCreditCard, FaUsers, FaCheck, FaTimes } from 'react-icons/fa';
import { LuNfc } from "react-icons/lu";

// Hardcoded card types with icons following established patterns
const HARDCODED_CARD_TYPES = [
    {
        id: 1,
        name: '600 x 400 BT 6Co',
        type_of_connection: 'bluetooth',
        setting: { width: 600, height: 400 },
        icon: <FaBluetooth size={20} />,
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    {
        id: 2,
        name: '416 x 240 NFC 4Co',
        type_of_connection: 'nfc',
        setting: { width: 416, height: 240 },
        icon: <LuNfc size={20} />,
        gradient: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)'
    },
    {
        id: 3,
        name: '300 x 400 NFC 4Co',
        type_of_connection: 'nfc',
        setting: { width: 300, height: 400 },
        icon: <LuNfc size={20} />,
        gradient: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)'
    },
    {
        id: 4,
        name: '300 x 400 NFC 3Co',
        type_of_connection: 'nfc',
        setting: { width: 300, height: 400 },
        icon: <LuNfc size={20} />,
        gradient: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)'
    }
];

const GroupForm = ({
    isModalOpen,
    setIsModalOpen,
    onSuccess,
    groupToEdit = null,
    preselectedMembers = []
}) => {
    const { isMobile } = useLayout();
    const navigate = useNavigate();
    const isEditMode = Boolean(groupToEdit?.id);

    const getUserDisplayName = useCallback((user) => {
        return user?.name || user?.username || user?.email || `User ${user?.id}`;
    }, []);

    const getInitialFormData = useCallback(() => ({
        groupName: "",
        groupDescription: "",
        selectedMembers: [],
        groupStatus: "active", // Set default status
        cardTypeId: null, // Add new field for card type
        pendingCardAssignments: {}, // Add pending card assignments
    }), []);

    const [formData, setFormData] = useState(getInitialFormData());
    const [potentialMembers, setPotentialMembers] = useState([]);
    const [selectedCardType, setSelectedCardType] = useState(null);
    const [statusOptions] = useState([
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
    ]);
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const [fetchingMembers, setFetchingMembers] = useState(false);
    const [isInitialized, setIsInitialized] = useState(false);

    // Card type selection handler
    const handleCardTypeSelect = useCallback((cardType) => {
        setSelectedCardType(cardType);
        handleChange("cardTypeId", cardType.id);
    }, []);



    const fetchAllUsers = useCallback(async () => {
        try {
            const response = await axiosInstance.get('datatable/users/view', {
                headers: {
                    Accept: "application/json",
                }
            });

            const usersData = response.data;
            console.log("Raw users data from API:", usersData);

            let users = [];
            if (Array.isArray(usersData)) {
                users = usersData;
            } else if (usersData?.data && Array.isArray(usersData.data)) {
                users = usersData.data;
            } else if (usersData?.users && Array.isArray(usersData.users)) {
                users = usersData.users;
            }

            if (users.length === 0) {
                console.warn("No users found in the response");
                return [];
            }

            console.log("Processed users array:", users);

            const formattedUsers = users.map(user => ({
                id: user.id,
                name: getUserDisplayName(user),
            }));

            console.log("Formatted users for MultiSelect:", formattedUsers);
            setPotentialMembers(formattedUsers);
            return formattedUsers;
        } catch (error) {
            console.error("Error fetching users:", error);
            throw error;
        }
    }, [getUserDisplayName]);

    const fetchGroupDetails = useCallback(async (groupId) => {
        try {
            // Use axiosInstance instead of fetch directly
            const response = await axiosInstance.get(`groups/${groupId}?users=true`, {
                headers: {
                    Accept: "application/json",
                }
            });

            return response.data;
        } catch (error) {
            console.error("Error fetching group details:", error);

            // Show error in toast
            return {
                data: {
                    id: groupId,
                    title: "Group Information Unavailable",
                    description: "Could not load group details. Please try again later.",
                    status: "active",
                    users: []
                }
            };
        }
    }, []);

    const initializeForm = useCallback(async () => {
        if (isInitialized) return;

        setFetchingMembers(true);
        try {
            // Fetch users
            const formattedUsers = await fetchAllUsers();

            // Initialize card types only once
            if (!selectedCardType && !isEditMode) {
                const defaultCardType = HARDCODED_CARD_TYPES[0];
                setSelectedCardType(defaultCardType);
                setFormData(prev => ({ ...prev, cardTypeId: defaultCardType.id }));
            }

            // Handle preselected members
            if (preselectedMembers?.length > 0) {
                const missingMembers = preselectedMembers.filter(preselected =>
                    !formattedUsers.some(user => user.id === preselected.id)
                );

                if (missingMembers.length > 0) {
                    const formattedMissingMembers = missingMembers.map(member => ({
                        id: member.id,
                        name: getUserDisplayName(member)
                    }));
                    setPotentialMembers(prev => [...prev, ...formattedMissingMembers]);
                }
            }

            // If in edit mode, fetch group details
            if (isEditMode && groupToEdit?.id) {
                const groupDetails = await fetchGroupDetails(groupToEdit.id);
                const currentGroup = groupDetails.data || groupDetails;

                // Find the matching card type from hardcoded types
                const matchingCardType = HARDCODED_CARD_TYPES.find(ct => ct.id === currentGroup.card_type_id);
                if (matchingCardType) {
                    setSelectedCardType(matchingCardType);
                }

                // Update form data with group details
                setFormData(prev => ({
                    ...prev,
                    groupName: currentGroup.title || "",
                    groupDescription: currentGroup.description || "",
                    selectedMembers: currentGroup.users?.map(user => ({
                        id: user.id,
                        name: getUserDisplayName(user)
                    })) || [],
                    groupStatus: currentGroup.status || "active",
                    cardTypeId: currentGroup.card_type_id || HARDCODED_CARD_TYPES[0]?.id,
                    pendingCardAssignments: groupToEdit.pendingCardAssignments || {},
                }));

                // Update potential members with group users
                if (currentGroup.users?.length > 0) {
                    const groupUsers = currentGroup.users.map(user => ({
                        id: user.id,
                        name: getUserDisplayName(user)
                    }));
                    setPotentialMembers(prev => {
                        const existingIds = new Set(prev.map(u => u.id));
                        const newUsers = groupUsers.filter(u => !existingIds.has(u.id));
                        return [...prev, ...newUsers];
                    });
                }
            } else if (preselectedMembers?.length > 0) {
                // Update form data with preselected members
                setFormData(prev => ({
                    ...prev,
                    selectedMembers: preselectedMembers.map(member => ({
                        id: member.id,
                        name: getUserDisplayName(member)
                    }))
                }));
            }

            setIsInitialized(true);
        } catch (error) {
            console.error("Error initializing form:", error);
            setErrors(prev => ({ ...prev, members: "Could not load data." }));
        } finally {
            setFetchingMembers(false);
        }
    }, [
        isEditMode,
        groupToEdit?.id,
        fetchAllUsers,
        fetchGroupDetails,
        getUserDisplayName,
        preselectedMembers,
        isInitialized,
        selectedCardType
    ]);

    useEffect(() => {
        if (isModalOpen && !isInitialized) {
            initializeForm();
        }
        return () => {
            if (!isModalOpen) {
                setIsInitialized(false);
                setErrors({});
                setFormData(getInitialFormData());
            }
        };
    }, [isModalOpen, initializeForm, isInitialized, getInitialFormData]);

    const handleChange = useCallback((name, value) => {
        console.log(`Updating ${name} with value:`, value);
        setFormData(prev => {
            const newData = {
                ...prev,
                [name]: value,
            };
            console.log('New form data:', newData);
            return newData;
        });

        if (errors[name]) {
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[name];
                return newErrors;
            });
        }
    }, [errors]);

    const validateForm = useCallback(() => {
        const newErrors = {};

        if (!formData.groupName?.trim()) newErrors.groupName = "Group name is required.";
        if (!formData.groupDescription?.trim()) newErrors.groupDescription = "Group description is required.";

        if (!fetchingMembers) {
            if (formData.selectedMembers?.length === 0) {
                newErrors.selectedMembers = errors.members
                    ? "Cannot select members; list failed to load."
                    : "At least one member must be selected.";
            }
        }

        if (!formData.groupStatus) newErrors.groupStatus = "Group status is required.";
        if (!formData.cardTypeId) newErrors.cardTypeId = "Card type is required.";

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [formData, fetchingMembers, errors.members]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!validateForm()) return;

        setLoading(true);
        try {
            const payload = {
                title: formData.groupName.trim(),
                description: formData.groupDescription.trim(),
                status: formData.groupStatus,
                card_type_id: formData.cardTypeId,
                user_ids: formData.selectedMembers.map(member => member.id),
                print_status: "unprinted",
                group_type: "regular",
                parent_group_id: null,
            };

            console.log('Submitting payload:', payload);

            let response;
            if (isEditMode) {
                response = await axiosInstance.put(`groups/${groupToEdit.id}`, payload);
                console.log('Edit response:', response.data);
            } else {
                response = await axiosInstance.post('groups', payload);
                console.log('Create response:', response.data);
            }

            // Close modal and call onSuccess with the response data
            setIsModalOpen(false);
            
            if (onSuccess) {
                console.log('Calling onSuccess with data:', {
                    ...response.data,
                    selectedMembers: formData.selectedMembers
                });
                // Pass the complete response data to onSuccess
                onSuccess({
                    ...response.data,
                    selectedMembers: formData.selectedMembers // Include selected members in the response
                });
            }
            
            // Navigate to users/groups page after successful creation (only for new groups)
            if (!isEditMode) {
                setTimeout(() => {
                    navigate('/users/groups');
                }, 100); // Wait 2 seconds to show the success message
            }
            
        } catch (err) {
            console.error("Submission error:", err);
            // toast?.show({
            //     severity: 'error',
            //     summary: 'Error',
            //     detail: err.response?.data?.message || err.message || 'Failed to save group',
            //     life: 5000
            // });
        } finally {
            setLoading(false);
        }

    };

    // Modern Card Type Selection Component
    const CardTypeSelector = () => (
        <div className="space-y-4">
            <label className="block text-lg font-semibold text-gray-800 mb-4">
                Select Card Type <span className="text-red-500">*</span>
            </label>
            <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'}`}>
                {HARDCODED_CARD_TYPES.map((cardType) => (
                    <motion.div
                        key={cardType.id}
                        className={`relative cursor-pointer rounded-xl p-4 border-2 transition-all duration-300 ${
                            selectedCardType?.id === cardType.id
                                ? 'border-[#00c3ac] bg-[#00c3ac]/5 shadow-lg'
                                : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                        }`}
                        onClick={() => handleCardTypeSelect(cardType)}
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        {/* Selection indicator */}
                        {selectedCardType?.id === cardType.id && (
                            <motion.div
                                className="absolute -top-2 -right-2 w-6 h-6 bg-[#00c3ac] rounded-full flex items-center justify-center"
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                            >
                                <FaCheck className="text-white text-xs" />
                            </motion.div>
                        )}

                        {/* Card preview */}
                        <div
                            className="w-full h-20 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden"
                            style={{ background: cardType.gradient }}
                        >
                            <div className="text-white text-center">
                                {cardType.icon}
                                <div className="text-xs mt-1 opacity-90">
                                    {cardType.setting.width} × {cardType.setting.height}
                                </div>
                            </div>
                        </div>

                        {/* Card info */}
                        <div className="text-center">
                            <h4 className="font-semibold text-gray-800 text-sm mb-1">
                                {cardType.name}
                            </h4>
                            <p className="text-xs text-gray-600 capitalize">
                                {cardType.type_of_connection}
                            </p>
                        </div>
                    </motion.div>
                ))}
            </div>
            {errors.cardTypeId && (
                <motion.div
                    className="text-red-500 text-sm mt-2"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                >
                    {errors.cardTypeId}
                </motion.div>
            )}
        </div>
    );

    const renderFooter = () => {
        const submitLabel = isEditMode
            ? (loading ? "Updating..." : "Update Group")
            : (loading ? "Creating..." : "Create Group");

        return (
            <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-end gap-2'} pt-6 border-t border-gray-100`}>
                <motion.button
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                    disabled={loading}
                    className={`px-6 py-3 bg-white text-gray-700 font-medium border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 hover:shadow-md hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 flex items-center gap-2 ${isMobile ? 'w-full justify-center' : ''} disabled:opacity-50 disabled:cursor-not-allowed`}
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                >
                    <FaTimes className="text-sm" />
                    <span>Cancel</span>
                </motion.button>
                <motion.button
                    type="submit"
                    form="group-form"
                    disabled={loading || fetchingMembers}
                    className={`px-6 py-3 bg-gradient-to-r from-[#00c3ac] to-[#02aa96] text-white font-semibold rounded-lg hover:from-[#02aa96] hover:to-[#00c3ac] hover:shadow-lg hover:transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 flex items-center gap-2 ${isMobile ? 'w-full justify-center' : ''} disabled:opacity-50 disabled:cursor-not-allowed`}
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                >
                    {loading ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                        <FaCheck className="text-sm" />
                    )}
                    <span>{submitLabel}</span>
                </motion.button>
            </div>
        );
    };

    return (
        <>
            <Dialog
                header={
                    <div className="flex items-center gap-3 pb-2">
                        <div className="w-10 h-10 bg-gradient-to-br from-[#00c3ac] to-[#02aa96] rounded-lg flex items-center justify-center">
                            <FaUsers className="text-white text-lg" />
                        </div>
                        <div>
                            <h2 className="text-xl font-bold text-gray-800">
                                {isEditMode ? "Edit Group" : "Create New Group"}
                            </h2>
                            <p className="text-sm text-gray-600">
                                {isEditMode ? "Update group information and settings" : "Set up a new group with members and card type"}
                            </p>
                        </div>
                    </div>
                }
                visible={isModalOpen}
                style={isMobile ? { width: "95vw", height: "95vh" } : { width: "70vw", maxWidth: '800px', minWidth: '600px' }}
                breakpoints={{ '960px': '85vw', '641px': '95vw' }}
                modal
                className="modern-group-dialog"
                onHide={() => !loading && setIsModalOpen(false)}
                footer={renderFooter()}
                closeOnEscape={!loading}
                maximizable={false}
                resizable={false}
                contentClassName="p-0"
            >
                <div className="p-6">
                    <AnimatePresence>
                        <motion.form
                            onSubmit={handleSubmit}
                            id="group-form"
                            className="space-y-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                        >
                            {errors.message && (
                                <motion.div
                                    className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-center text-sm"
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    {errors.message}
                                </motion.div>
                            )}

                            {/* Card Type Selection - At the top */}
                            <CardTypeSelector />

                            {/* Group Name */}
                            <div className="space-y-2">
                                <label htmlFor="groupName" className="block text-sm font-semibold text-gray-700">
                                    Group Name <span className="text-red-500">*</span>
                                </label>
                                <div className="relative">
                                    <InputText
                                        id="groupName"
                                        value={formData.groupName}
                                        onChange={(e) => handleChange("groupName", e.target.value)}
                                        placeholder="Enter a descriptive group name"
                                        className={`w-full px-4 py-3 border rounded-lg transition-all duration-200 ${
                                            errors.groupName
                                                ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                                                : 'border-gray-300 focus:border-[#00c3ac] focus:ring-[#00c3ac]/20'
                                        } focus:ring-4 focus:outline-none`}
                                        disabled={loading}
                                        autoFocus
                                    />
                                </div>
                                {errors.groupName && (
                                    <motion.div
                                        className="text-red-500 text-sm flex items-center gap-1"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                    >
                                        <span>⚠️</span>
                                        {errors.groupName}
                                    </motion.div>
                                )}
                            </div>

                            {/* Group Description */}
                            <div className="space-y-2">
                                <label htmlFor="groupDescription" className="block text-sm font-semibold text-gray-700">
                                    Group Description <span className="text-red-500">*</span>
                                </label>
                                <div className="relative">
                                    <InputTextarea
                                        id="groupDescription"
                                        value={formData.groupDescription}
                                        onChange={(e) => handleChange("groupDescription", e.target.value)}
                                        rows={isMobile ? 3 : 4}
                                        placeholder="Describe the purpose and details of this group"
                                        className={`w-full px-4 py-3 border rounded-lg transition-all duration-200 resize-none ${
                                            errors.groupDescription
                                                ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                                                : 'border-gray-300 focus:border-[#00c3ac] focus:ring-[#00c3ac]/20'
                                        } focus:ring-4 focus:outline-none`}
                                        autoResize
                                        disabled={loading}
                                    />
                                </div>
                                {errors.groupDescription && (
                                    <motion.div
                                        className="text-red-500 text-sm flex items-center gap-1"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                    >
                                        <span>⚠️</span>
                                        {errors.groupDescription}
                                    </motion.div>
                                )}
                            </div>

                            {/* Member Selection - Overhauled Design */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <label className="block text-sm font-semibold text-gray-700">
                                        Select Team Members <span className="text-red-500">*</span>
                                    </label>
                                    <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                        {formData.selectedMembers.length} / {potentialMembers.length} selected
                                    </div>
                                </div>

                                {/* Search and Filter Bar */}
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <FaUsers className="h-4 w-4 text-gray-400" />
                                    </div>
                                    <MultiSelect
                                        id="selectedMembers"
                                        value={formData.selectedMembers}
                                        options={potentialMembers}
                                        onChange={(e) => handleChange("selectedMembers", e.value)}
                                        optionLabel="name"
                                        dataKey="id"
                                        placeholder={fetchingMembers
                                            ? "Loading team members..."
                                            : potentialMembers.length === 0
                                                ? "No members available"
                                                : "Search and select team members..."}
                                        filter
                                        display="chip"
                                        className={`w-full modern-multiselect ${
                                            errors.selectedMembers
                                                ? 'border-red-300 focus:border-red-500'
                                                : 'border-gray-300 focus:border-[#00c3ac]'
                                        }`}
                                        disabled={loading || fetchingMembers}
                                        loading={fetchingMembers}
                                        maxSelectedLabels={isMobile ? 2 : 4}
                                        selectedItemsLabel={`${formData.selectedMembers.length} member${formData.selectedMembers.length !== 1 ? 's' : ''} selected`}
                                        style={{
                                            minHeight: '52px',
                                            borderRadius: '12px',
                                            border: errors.selectedMembers ? '2px solid #fca5a5' : '2px solid #e5e7eb',
                                            paddingLeft: '40px'
                                        }}
                                        panelStyle={{
                                            borderRadius: '12px',
                                            border: '1px solid #e5e7eb',
                                            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                                        }}
                                        filterPlaceholder="Search members..."
                                    />
                                    {fetchingMembers && (
                                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                                            <div className="w-5 h-5 border-2 border-[#00c3ac] border-t-transparent rounded-full animate-spin"></div>
                                        </div>
                                    )}
                                </div>

                                {/* Error Message */}
                                {errors.selectedMembers && (
                                    <motion.div
                                        className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center gap-2"
                                        initial={{ opacity: 0, scale: 0.95 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <div className="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                                            <span className="text-red-600 text-xs">!</span>
                                        </div>
                                        <span className="text-sm">{errors.selectedMembers}</span>
                                    </motion.div>
                                )}

                                {/* Selected Members Summary */}
                                {formData.selectedMembers.length > 0 && (
                                    <motion.div
                                        className="bg-gradient-to-r from-[#00c3ac]/5 to-[#02aa96]/5 border border-[#00c3ac]/20 rounded-xl p-4"
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <div className="flex items-center justify-between mb-3">
                                            <div className="flex items-center gap-2">
                                                <div className="w-8 h-8 bg-[#00c3ac] rounded-lg flex items-center justify-center">
                                                    <FaUsers className="text-white text-sm" />
                                                </div>
                                                <div>
                                                    <h4 className="font-semibold text-gray-800 text-sm">Selected Members</h4>
                                                    <p className="text-xs text-gray-600">
                                                        {formData.selectedMembers.length} member{formData.selectedMembers.length !== 1 ? 's' : ''} will be added to this group
                                                    </p>
                                                </div>
                                            </div>
                                            <button
                                                type="button"
                                                onClick={() => handleChange("selectedMembers", [])}
                                                className="text-xs text-gray-500 hover:text-red-500 transition-colors duration-200 px-2 py-1 rounded hover:bg-red-50"
                                                disabled={loading}
                                            >
                                                Clear All
                                            </button>
                                        </div>

                                        {/* Member List Preview */}
                                        <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                                            {formData.selectedMembers.slice(0, 5).map((member) => (
                                                <div key={member.id} className="flex items-center justify-between bg-white rounded-lg px-3 py-2 border border-gray-100">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                                            <span className="text-white text-xs font-semibold">
                                                                {member.name.charAt(0).toUpperCase()}
                                                            </span>
                                                        </div>
                                                        <span className="text-sm text-gray-700 font-medium">{member.name}</span>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        onClick={() => {
                                                            const updatedMembers = formData.selectedMembers.filter(m => m.id !== member.id);
                                                            handleChange("selectedMembers", updatedMembers);
                                                        }}
                                                        className="text-gray-400 hover:text-red-500 transition-colors duration-200 p-1"
                                                        disabled={loading}
                                                    >
                                                        <FaTimes className="text-xs" />
                                                    </button>
                                                </div>
                                            ))}
                                            {formData.selectedMembers.length > 5 && (
                                                <div className="text-center py-2 text-xs text-gray-500">
                                                    ... and {formData.selectedMembers.length - 5} more member{formData.selectedMembers.length - 5 !== 1 ? 's' : ''}
                                                </div>
                                            )}
                                        </div>
                                    </motion.div>
                                )}
                            </div>

                            {/* Group Status */}
                            <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
                                <div className="space-y-2">
                                    <label htmlFor="groupStatus" className="block text-sm font-semibold text-gray-700">
                                        Group Status
                                    </label>
                                    <div className="relative">
                                        <Dropdown
                                            id="groupStatus"
                                            value={formData.groupStatus}
                                            options={statusOptions}
                                            onChange={(e) => handleChange("groupStatus", e.value)}
                                            optionLabel="label"
                                            optionValue="value"
                                            placeholder="Select status"
                                            className="w-full"
                                            disabled={loading}
                                            style={{
                                                height: '48px',
                                                borderRadius: '8px',
                                                border: '1px solid #d1d5db'
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </motion.form>
                    </AnimatePresence>
                </div>
            </Dialog>
        </>
    );
};

GroupForm.propTypes = {
    isModalOpen: PropTypes.bool.isRequired,
    setIsModalOpen: PropTypes.func.isRequired,
    onSuccess: PropTypes.func,
    groupToEdit: PropTypes.object,
    preselectedMembers: PropTypes.array
};

export default GroupForm;